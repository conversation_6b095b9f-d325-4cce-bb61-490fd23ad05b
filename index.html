<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>QR Anatomy Story</title>
    <!-- Disable WebXR sessions -->
    <meta http-equiv="Permissions-Policy" content="xr=()" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <!-- A-Frame core -->
    <script src="https://aframe.io/releases/1.5.0/aframe.min.js"></script>
    <style>
      html, body { margin: 0; height: 100%; overflow: hidden; }
      /* Bottom story overlay */
      #storyBox {
        position: absolute; bottom: 0; left: 0; right: 0;
        padding: 1rem .75rem; background: rgba(0,0,0,.6);
        color: #fff; font-family: system-ui, sans-serif;
        font-size: 1rem; line-height: 1.35;
        text-align: center; max-height: 35vh; overflow-y: auto;
        backdrop-filter: blur(4px);
      }
      @media (min-width: 600px) {
        #storyBox { font-size: 1.1rem; }
      }
    </style>
  </head>
  <body>
    <div id="storyBox">Tap a body part to read its story …</div>

    <a-scene
      embedded
      renderer="antialias: true; colorManagement: true"
      vr-mode-ui="enabled: false"
      webxr="enabled: false"
    >
      <!-- Camera rig with locked controls and cursor on camera for clickable detection -->
      <a-entity id="cameraRig" position="0 0 4">
        <a-camera
          look-controls="enabled: false"
          cursor="rayOrigin: mouse"
          raycaster="objects: .clickable"
        ></a-camera>
      </a-entity>

      <a-assets>
        <img id="img-body" src="https://cdn.glitch.global/3431c86e-14ce-4fb6-9c42-5a63d60e5f49/body.png?v=1748526343876" alt="Body drawing" />
        <img id="img-hand" src="https://cdn.glitch.global/3431c86e-14ce-4fb6-9c42-5a63d60e5f49/hand.png?v=1748526355100" alt="Hand drawing" />
        <img id="img-foot" src="https://cdn.glitch.global/3431c86e-14ce-4fb6-9c42-5a63d60e5f49/foot.png?v=1748526353539" alt="Foot drawing" />
        <img id="img-head" src="https://cdn.glitch.global/3431c86e-14ce-4fb6-9c42-5a63d60e5f49/head.png?v=1748526356676" alt="Head drawing" />
      </a-assets>

      <!-- Unlit planes that don’t react to scene lights -->
      <a-plane id="hand" class="clickable"
        material="shader: flat; src: #img-hand; side: double; transparent: true"
        position="-1 0 -2" scale="1.2 1.2 1"
        part-story="name: hand; text: I once broke my hand falling off a bicycle. Seven weeks in plaster taught me patience and respect for fragility."
      ></a-plane>

      <a-plane id="foot" class="clickable"
        material="shader: flat; src: #img-foot; side: double; transparent: true"
        position="0 0 -2.6" scale="1.2 1.2 1"
        part-story="name: foot; text: My right foot still bears the scar from stepping on a seashell at ten—the memory of seaside summers written in tissue."
      ></a-plane>

      <a-plane id="head" class="clickable"
        material="shader: flat; src: #img-head; side: double; transparent: true"
        position="1 0 -3.2" scale="1.2 1.2 1"
        part-story="name: head; text: A faint dent above my eyebrow from a childhood tree-climb reminds me that curiosity can leave gentle marks."
      ></a-plane>
    </a-scene>

    <script>
      AFRAME.registerComponent('part-story', {
        schema: { name: { type: 'string' }, text: { type: 'string' } },
        init() { this.el.addEventListener('click', () => setActivePart(this.el)); }
      });
      const OTHER_OPACITY = 0.25;
      const ACTIVE_Z = -1.4;
      const INACTIVE_Z = -3.0;
      const ACTIVE_SCALE = 1.45;
      const INACTIVE_SCALE = 1.0;
      function setActivePart(activeEl) {
        document.querySelectorAll('[part-story]').forEach(el => {
          const isActive = el === activeEl;
          el.setAttribute('animation__opacity', {
            property: 'material.opacity', to: isActive ? 1 : OTHER_OPACITY,
            dur: 300, easing: 'easeOutQuad'
          });
          el.setAttribute('animation__z', {
            property: 'position.z', to: isActive ? ACTIVE_Z : INACTIVE_Z,
            dur: 300, easing: 'easeOutQuad'
          });
          const scaleVal = isActive ? ACTIVE_SCALE : INACTIVE_SCALE;
          el.setAttribute('animation__scale', {
            property: 'scale', to: `${scaleVal} ${scaleVal} 1`,
            dur: 300, easing: 'easeOutQuad'
          });
        });
        const storyText = activeEl.components['part-story'].data.text;
        document.getElementById('storyBox').textContent = storyText;
      }
    </script>
  </body>
</html>
