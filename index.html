<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>QR Anatomy Story</title>
    <!-- Disable WebXR sessions -->
    <meta http-equiv="Permissions-Policy" content="xr=()" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <!-- A-Frame core -->
    <script src="https://aframe.io/releases/1.5.0/aframe.min.js"></script>
    <style>
      html,
      body {
        margin: 0;
        height: 100%;
        overflow: hidden;
      }
      /* Bottom story overlay */
      #storyBox {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1rem 0.75rem;
        background: rgba(0, 0, 0, 0.6);
        color: #fff;
        font-family: system-ui, sans-serif;
        font-size: 1rem;
        line-height: 1.35;
        text-align: center;
        max-height: 35vh;
        overflow-y: auto;
        backdrop-filter: blur(4px);
      }
      @media (min-width: 600px) {
        #storyBox {
          font-size: 1.1rem;
        }
      }
    </style>
  </head>
  <body>
    <div id="storyBox">Tap a body part to read its story …</div>

    <a-scene
      embedded
      renderer="antialias: true; colorManagement: true"
      vr-mode-ui="enabled: false"
      webxr="enabled: false"
    >
      <!-- Camera rig with locked controls and cursor on camera for clickable detection -->
      <a-entity id="cameraRig" position="0 0 4">
        <a-camera
          look-controls="enabled: false"
          cursor="rayOrigin: mouse"
          raycaster="objects: .clickable"
        ></a-camera>
      </a-entity>

      <a-assets>
        <img
          id="img-body"
          src="https://cdn.glitch.global/3431c86e-14ce-4fb6-9c42-5a63d60e5f49/body.png?v=1748526343876"
          alt="Body drawing"
        />
        <img
          id="img-hand"
          src="https://cdn.glitch.global/3431c86e-14ce-4fb6-9c42-5a63d60e5f49/hand.png?v=1748526355100"
          alt="Hand drawing"
        />
        <img
          id="img-foot"
          src="https://cdn.glitch.global/3431c86e-14ce-4fb6-9c42-5a63d60e5f49/foot.png?v=1748526353539"
          alt="Foot drawing"
        />
        <img
          id="img-head"
          src="https://cdn.glitch.global/3431c86e-14ce-4fb6-9c42-5a63d60e5f49/head.png?v=1748526356676"
          alt="Head drawing"
        />
      </a-assets>

      <!-- Unlit planes that don’t react to scene lights -->

 <a-plane id="hand" class="clickable"
        material="shader: flat; src: #img-hand; side: double; transparent: true"
        position="0.45641 -0.33425 -2"
        scale="0.45 0.97 1"
        part-story="name: hand; text: I once broke my hand falling off a bicycle. Seven weeks in plaster taught me patience and respect for fragility."
        rotation="0 0 180"
      ></a-plane>

      <a-plane id="foot" class="clickable"
        material="shader: flat; src: #img-foot; side: double; transparent: true"
        position="-0.22764 -2.38455 -2"
        scale="0.48 0.49 1"
        part-story="name: foot; text: My right foot still bears the scar from stepping on a seashell at ten—the memory of seaside summers written in tissue."
      ></a-plane>

      <a-plane id="head" class="clickable"
        material="shader: flat; src: #img-head; side: double; transparent: true"
        position="0.02193 2.99287 -2"
        scale="0.73 1.2 1"
        part-story="name: head; text: A faint dent above my eyebrow from a childhood tree-climb reminds me that curiosity can leave gentle marks."
      ></a-plane>

      <a-plane id="body" class="clickable"
        material="shader: flat; src: #img-body; side: double; transparent: true"
        position="0 0 -2"
        scale="1.2 5.23 1"
        part-story="name: body; text: My torso carries the invisible weight of every breath, every heartbeat, every moment of joy and sorrow."
      ></a-plane>
    </a-scene>

    <script>
      AFRAME.registerComponent("part-story", {
        schema: { name: { type: "string" }, text: { type: "string" } },
        init() {
          this.el.addEventListener("click", () => setActivePart(this.el));
          // Add touch support for mobile
          this.el.addEventListener("touchstart", (e) => {
            e.preventDefault();
            setActivePart(this.el);
          });
        },
      });

      const OTHER_OPACITY = 0.2;
      let currentActive = null;

      function setActivePart(activeEl) {
        const storyBox = document.getElementById("storyBox");

        // If clicking the same element again, deactivate it
        if (currentActive === activeEl) {
          resetAllParts();
          storyBox.textContent = "Tap a body part to read its story …";
          currentActive = null;
          return;
        }

        currentActive = activeEl;

        document.querySelectorAll("[part-story]").forEach((el) => {
          const isActive = el === activeEl;

          // Animate opacity with blur effect
          el.setAttribute("animation__opacity", {
            property: "material.opacity",
            to: isActive ? 1 : OTHER_OPACITY,
            dur: 400,
            easing: "easeOutQuad",
          });

          // Apply blur effect to inactive elements
          if (!isActive) {
            // Add blur through CSS filter on the mesh
            el.object3D.traverse((obj) => {
              if (obj.material) {
                obj.material.transparent = true;
                obj.material.opacity = OTHER_OPACITY;
                // Apply a slight blur effect by reducing material clarity
                if (obj.material.map) {
                  obj.material.map.minFilter = THREE.LinearFilter;
                  obj.material.map.magFilter = THREE.LinearFilter;
                }
              }
            });
          } else {
            // Remove blur from active element
            el.object3D.traverse((obj) => {
              if (obj.material) {
                obj.material.transparent = true;
                obj.material.opacity = 1;
                if (obj.material.map) {
                  obj.material.map.minFilter = THREE.NearestFilter;
                  obj.material.map.magFilter = THREE.LinearFilter;
                }
              }
            });
          }
        });

        const storyText = activeEl.components["part-story"].data.text;
        storyBox.textContent = storyText;
      }

      function resetAllParts() {
        document.querySelectorAll("[part-story]").forEach((el) => {
          el.setAttribute("animation__opacity", {
            property: "material.opacity",
            to: 1,
            dur: 400,
            easing: "easeOutQuad",
          });

          // Remove blur effects from all elements
          el.object3D.traverse((obj) => {
            if (obj.material) {
              obj.material.transparent = true;
              obj.material.opacity = 1;
              if (obj.material.map) {
                obj.material.map.minFilter = THREE.NearestFilter;
                obj.material.map.magFilter = THREE.LinearFilter;
              }
            }
          });
        });
      }

      // Initialize with all parts visible
      document.addEventListener("DOMContentLoaded", () => {
        resetAllParts();
      });
    </script>
  </body>
</html>
