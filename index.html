<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Interactive Anatomy Stories</title>
    <!-- Disable WebXR sessions -->
    <meta http-equiv="Permissions-Policy" content="xr=()" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <!-- A-Frame core -->
    <script src="https://aframe.io/releases/1.5.0/aframe.min.js"></script>
    <style>
      html, body {
        margin: 0;
        height: 100%;
        overflow: hidden;
        font-family: system-ui, -apple-system, sans-serif;
        background: #000;
      }

      /* Bottom story overlay */
      #storyBox {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1.5rem 1rem;
        background: linear-gradient(to top, rgba(0,0,0,0.9), rgba(0,0,0,0.7));
        color: #fff;
        font-size: 1rem;
        line-height: 1.4;
        text-align: center;
        max-height: 40vh;
        overflow-y: auto;
        backdrop-filter: blur(8px);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(100%);
        opacity: 0;
        border-top: 1px solid rgba(255,255,255,0.1);
      }

      #storyBox.active {
        opacity: 1;
        transform: translateY(0);
      }

      /* Instructions overlay */
      #instructions {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: rgba(255,255,255,0.8);
        text-align: center;
        font-size: 1.1rem;
        pointer-events: none;
        transition: opacity 0.3s ease;
        z-index: 10;
      }

      #instructions.hidden {
        opacity: 0;
      }

      /* Mobile optimizations */
      @media (max-width: 768px) {
        #storyBox {
          font-size: 0.9rem;
          padding: 1rem 0.75rem;
          max-height: 35vh;
        }
        #instructions {
          font-size: 1rem;
          padding: 0 1rem;
        }
      }

      @media (min-width: 768px) {
        #storyBox {
          font-size: 1.1rem;
          padding: 2rem 1.5rem;
        }
      }

      /* Loading indicator */
      .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 1.2rem;
      }
    </style>
  </head>
  <body>
    <!-- Instructions overlay -->
    <div id="instructions">
      <p>👆 Tap any body part to explore its story</p>
    </div>

    <!-- Story display area -->
    <div id="storyBox"></div>

    <a-scene
      embedded
      renderer="antialias: true; colorManagement: true; alpha: true"
      vr-mode-ui="enabled: false"
      webxr="enabled: false"
      background="color: #000"
    >
      <!-- Camera with mobile-friendly controls -->
      <a-entity id="cameraRig" position="0 0 3.5">
        <a-camera
          look-controls="enabled: true; touchEnabled: true; magicWindowTrackingEnabled: true"
          wasd-controls="enabled: false"
          cursor="rayOrigin: mouse; fuse: false"
          raycaster="objects: .clickable; far: 20; near: 0.1"
        ></a-camera>
      </a-entity>

      <!-- Assets -->
      <a-assets>
        <img id="img-hand" src="hand.png" alt="Hand drawing" crossorigin="anonymous" />
        <img id="img-foot" src="foot.png" alt="Foot drawing" crossorigin="anonymous" />
        <img id="img-head" src="head.png" alt="Head drawing" crossorigin="anonymous" />
      </a-assets>

      <!-- Unlit planes that don’t react to scene lights -->
      <a-plane id="hand" class="clickable body-part"
        material="shader: flat; src: #img-hand; side: double; transparent: true; alphaTest: 0.1"
        position="-1.2 0.3 -2"
        scale="1 1 1"
        rotation="0 0 0"
        part-story="name: hand; text: I once broke my hand falling off a bicycle during a summer evening ride. Seven weeks in plaster taught me patience and respect for the fragility of our bodies. The cast became a canvas for friends' signatures and doodles."
        animation__hover="property: scale; to: 1.05 1.05 1; startEvents: mouseenter; dur: 200"
        animation__unhover="property: scale; to: 1 1 1; startEvents: mouseleave; dur: 200"
      ></a-plane>

      <a-plane id="foot" class="clickable body-part"
        material="shader: flat; src: #img-foot; side: double; transparent: true; alphaTest: 0.1"
        position="0 -0.8 -2.3"
        scale="1 1 1"
        rotation="0 0 0"
        part-story="name: foot; text: My right foot still bears the scar from stepping on a sharp seashell at age ten. The memory of that seaside summer is written in tissue—a reminder of childhood adventures and the stories our bodies carry through time."
        animation__hover="property: scale; to: 1.05 1.05 1; startEvents: mouseenter; dur: 200"
        animation__unhover="property: scale; to: 1 1 1; startEvents: mouseleave; dur: 200"
      ></a-plane>

      <a-plane id="head" class="clickable body-part"
        material="shader: flat; src: #img-head; side: double; transparent: true; alphaTest: 0.1"
        position="1.2 1.1 -2.6"
        scale="1 1 1"
        rotation="0 0 0"
        part-story="name: head; text: A faint dent above my eyebrow from a childhood tree-climbing adventure reminds me that curiosity can leave gentle marks. That oak tree taught me about heights, courage, and the sweet ache of growing up."
        animation__hover="property: scale; to: 1.05 1.05 1; startEvents: mouseenter; dur: 200"
        animation__unhover="property: scale; to: 1 1 1; startEvents: mouseleave; dur: 200"
      ></a-plane>

      <!-- Ambient lighting for better visibility -->
      <a-light type="ambient" color="#404040" intensity="0.8"></a-light>
      <a-light type="directional" position="2 4 5" color="#ffffff" intensity="0.5"></a-light>
    </a-scene>

    <script>
      // Enhanced A-Frame component for interactive body parts
      AFRAME.registerComponent('part-story', {
        schema: {
          name: { type: 'string' },
          text: { type: 'string' }
        },
        init() {
          // Store original position and scale for reset
          this.originalPosition = this.el.getAttribute('position');
          this.originalScale = this.el.getAttribute('scale');

          // Click/touch event handlers
          this.el.addEventListener('click', (e) => {
            e.stopPropagation();
            setActivePart(this.el);
          });

          // Enhanced touch support for mobile
          this.el.addEventListener('touchstart', (e) => {
            e.preventDefault();
            e.stopPropagation();
            setActivePart(this.el);
          });

          // Hover effects for desktop
          this.el.addEventListener('mouseenter', () => {
            if (currentActive !== this.el) {
              this.el.setAttribute('animation__hover', {
                property: 'scale',
                to: '1.1 1.1 1',
                dur: 150,
                easing: 'easeOutQuad'
              });
            }
          });

          this.el.addEventListener('mouseleave', () => {
            if (currentActive !== this.el) {
              this.el.setAttribute('animation__hover', {
                property: 'scale',
                to: '1 1 1',
                dur: 150,
                easing: 'easeOutQuad'
              });
            }
          });
        }
      });

      // Enhanced blur effect component
      AFRAME.registerComponent('blur-effect', {
        schema: {
          intensity: { type: 'number', default: 0 }
        },
        init() {
          this.material = this.el.getObject3D('mesh').material;
        },
        update() {
          if (this.material) {
            // Apply blur through opacity and material properties
            const intensity = this.data.intensity;
            this.material.opacity = Math.max(0.15, 1 - intensity);
            this.material.transparent = true;
          }
        }
      });

      // Configuration constants
      const CONFIG = {
        BLUR_OPACITY: 0.15,
        ACTIVE_Z_OFFSET: 1.5,
        INACTIVE_Z_OFFSET: -1.0,
        ACTIVE_SCALE: 1.4,
        INACTIVE_SCALE: 0.85,
        ANIMATION_DURATION: 400,
        EASING: 'easeInOutCubic'
      };

      let currentActive = null;
      let isAnimating = false;

      function setActivePart(activeEl) {
        if (isAnimating) return;

        const storyBox = document.getElementById('storyBox');
        const instructions = document.getElementById('instructions');

        // Hide instructions on first interaction
        if (!instructions.classList.contains('hidden')) {
          instructions.classList.add('hidden');
        }

        // If clicking the same element again, deactivate it
        if (currentActive === activeEl) {
          resetAllParts();
          storyBox.classList.remove('active');
          currentActive = null;
          return;
        }

        isAnimating = true;
        currentActive = activeEl;

        // Apply depth-of-field effect to all parts
        document.querySelectorAll('.body-part').forEach(el => {
          const isActive = el === activeEl;
          const originalPos = el.components['part-story'].originalPosition;

          // Calculate new Z position for depth effect
          const newZ = isActive
            ? originalPos.z + CONFIG.ACTIVE_Z_OFFSET
            : originalPos.z + CONFIG.INACTIVE_Z_OFFSET;

          const newScale = isActive ? CONFIG.ACTIVE_SCALE : CONFIG.INACTIVE_SCALE;
          const newOpacity = isActive ? 1 : CONFIG.BLUR_OPACITY;

          // Animate position (depth)
          el.setAttribute('animation__depth', {
            property: 'position.z',
            to: newZ,
            dur: CONFIG.ANIMATION_DURATION,
            easing: CONFIG.EASING
          });

          // Animate scale
          el.setAttribute('animation__scale', {
            property: 'scale',
            to: `${newScale} ${newScale} 1`,
            dur: CONFIG.ANIMATION_DURATION,
            easing: CONFIG.EASING
          });

          // Animate opacity for blur effect
          el.setAttribute('animation__opacity', {
            property: 'material.opacity',
            to: newOpacity,
            dur: CONFIG.ANIMATION_DURATION,
            easing: CONFIG.EASING
          });

          // Add blur component to inactive elements
          if (!isActive) {
            el.setAttribute('blur-effect', 'intensity: 0.8');
          } else {
            el.removeAttribute('blur-effect');
          }
        });

        // Show story with enhanced animation
        const storyText = activeEl.components['part-story'].data.text;
        storyBox.textContent = storyText;
        storyBox.classList.add('active');

        // Reset animation flag after animation completes
        setTimeout(() => {
          isAnimating = false;
        }, CONFIG.ANIMATION_DURATION);
      }

      function resetAllParts() {
        if (isAnimating) return;

        isAnimating = true;

        document.querySelectorAll('.body-part').forEach(el => {
          const originalPos = el.components['part-story'].originalPosition;
          const originalScale = el.components['part-story'].originalScale;

          // Reset to original positions and properties
          el.setAttribute('animation__depth', {
            property: 'position.z',
            to: originalPos.z,
            dur: CONFIG.ANIMATION_DURATION,
            easing: CONFIG.EASING
          });

          el.setAttribute('animation__scale', {
            property: 'scale',
            to: `${originalScale.x} ${originalScale.y} ${originalScale.z}`,
            dur: CONFIG.ANIMATION_DURATION,
            easing: CONFIG.EASING
          });

          el.setAttribute('animation__opacity', {
            property: 'material.opacity',
            to: 1,
            dur: CONFIG.ANIMATION_DURATION,
            easing: CONFIG.EASING
          });

          // Remove blur effect
          el.removeAttribute('blur-effect');
        });

        setTimeout(() => {
          isAnimating = false;
        }, CONFIG.ANIMATION_DURATION);
      }

      // Initialize when DOM is ready
      document.addEventListener('DOMContentLoaded', () => {
        // Set up click-outside-to-reset functionality
        document.addEventListener('click', (e) => {
          if (!e.target.closest('.body-part') && currentActive) {
            resetAllParts();
            document.getElementById('storyBox').classList.remove('active');
            currentActive = null;
          }
        });

        // Prevent context menu on long press (mobile)
        document.addEventListener('contextmenu', (e) => {
          if (e.target.closest('.body-part')) {
            e.preventDefault();
          }
        });

        console.log('Interactive Anatomy Stories initialized');
      });
    </script>
  </body>
</html>
